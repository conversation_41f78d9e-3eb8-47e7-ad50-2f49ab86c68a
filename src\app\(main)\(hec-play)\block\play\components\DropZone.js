import React from 'react';

const DropZone = ({
  word = null,
  index,
  blockIndex,
  hide = false,
  disable = false,
  disableDrop = false,
  disableRemove = false,
  dragType,
  onDrop,
  onRemoveWord,
  className = "",
  style = {}
}) => {
  if (hide) return null;

  const handleDragOver = (e) => {
    if (disable || disableDrop) {
      return;
    }
    e.preventDefault();
  };

  const handleDrop = (e) => {
    if (disable || disableDrop) {
      e.preventDefault();
      return;
    }
    if (onDrop) {
      onDrop(dragType, index, blockIndex);
    }
  };

  const handleRemoveWord = () => {
    if (disable || disableRemove || !word) {
      return;
    }
    if (onRemoveWord) {
      onRemoveWord(index, dragType, blockIndex);
    }
  };

  const getClassName = () => {
    let baseClass = "w-16 h-10 bg-[#FFFDE8] border border-dashed border-orange-400 rounded-md flex items-center justify-center text-sm";
    
    if (disable) {
      baseClass += " opacity-50";
    }
    
    if (className) {
      baseClass += ` ${className}`;
    }
    
    return baseClass;
  };

  return (
    <div
      onDragOver={handleDragOver}
      onDrop={handleDrop}
      className={getClassName()}
      style={style}
    >
      {word ? (
        <span
          onClick={handleRemoveWord}
          className={`${disable || disableRemove ? 'cursor-default' : 'cursor-pointer'}`}
        >
          {word} ×
        </span>
      ) : null}
    </div>
  );
};

export default DropZone;
