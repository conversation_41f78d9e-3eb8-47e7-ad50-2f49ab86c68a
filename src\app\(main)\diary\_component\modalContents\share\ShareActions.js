import React from 'react';
import { Icon } from '@iconify/react';

const ShareActions = ({ 
  isCopied, 
  isGeneratingLink, 
  onCopyLink, 
  onGenerateLink 
}) => {
  return (
    <div className="flex justify-between items-center mb-4">
      <button
        className={`flex items-center gap-1 px-4 py-1 rounded-md transition-all duration-200 ${
          isCopied
            ? 'bg-green-100 text-green-700 border border-green-300'
            : 'border border-yellow-500 hover:bg-gray-50'
        }`}
        onClick={onCopyLink}
      >
        <Icon
          icon={isCopied ? 'mdi:check' : 'material-symbols:add-link-rounded'}
          width="24"
          height="24"
          className={isCopied ? 'text-green-600' : ''}
        />
        {isCopied ? 'Copied!' : 'Copy Link'}
      </button>

      <button
        className="bg-gray-800 text-white px-6 py-2 rounded-md hover:bg-gray-700 flex items-center gap-2"
        onClick={onGenerateLink}
        disabled={isGeneratingLink}
      >
        {isGeneratingLink ? (
          <>
            <span className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2"></span>
            Generating...
          </>
        ) : (
          'Generate Link'
        )}
      </button>
    </div>
  );
};

export default ShareActions;
