'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'sonner';
import api from '@/lib/api';

const EssayFeedbackModal = ({
  isOpen,
  onClose,
  submissionId,
  existingFeedback = '',
  existingPoints = '',
  onSuccess,
}) => {
  const [feedback, setFeedback] = useState(existingFeedback);
  const [points, setPoints] = useState(existingPoints);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Update local state when props change
  useEffect(() => {
    setFeedback(existingFeedback);
    setPoints(existingPoints);
  }, [existingFeedback, existingPoints]);

  // Handle feedback submission
  const handleSubmit = async () => {
    if (!feedback.trim()) {
      toast.error('Please enter feedback before submitting');
      return;
    }

    if (!points || points < 0 || points > 100) {
      toast.error('Please enter a valid score between 0 and 100');
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await api.patch(`/tutor-essay/markEssay`, {
        submissionId,
        points: parseInt(points),
        submissionFeedback: feedback.trim(),
      });

      if (response.success) {
        toast.success('Feedback updated successfully');
        setFeedback('');
        setPoints('');
        if (onSuccess) {
          onSuccess();
        }
        onClose();
      } else {
        throw new Error(response.message || 'Failed to update feedback');
      }
    } catch (error) {
      console.error('Error updating feedback:', error);
      toast.error(error.message || 'Failed to update feedback');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (!isSubmitting) {
      setFeedback(existingFeedback);
      setPoints(existingPoints);
      onClose();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={handleClose}
        >
          <motion.div
            className="bg-white rounded-xl shadow-xl w-full max-w-2xl overflow-hidden relative"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: 'spring', damping: 20, stiffness: 300 }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Wooden sign header */}
            <div className="relative">
              <div className="bg-[#FFF9FB] pt-2 pb-2 px-2 shadow-xl flex justify-center">
                <Image
                  src="/assets/images/all-img/wooden-feedback-sign.png"
                  alt="Teacher's Feedback"
                  width={300}
                  height={80}
                  priority
                />
              </div>
            </div>

            {/* Modal content */}
            <div className="p-6">
              {/* Score Input */}
              <div className="mb-4">
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Score (0-100)
                </label>
                <input
                  type="number"
                  value={points}
                  onChange={(e) => setPoints(e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg"
                  placeholder="Enter score"
                  min="0"
                  max="100"
                  disabled={isSubmitting}
                />
              </div>

              {/* Feedback Input */}
              <div className="mb-4">
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Teacher's Feedback
                </label>
                <textarea
                  value={feedback}
                  onChange={(e) => setFeedback(e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg h-32"
                  placeholder="Write your feedback here..."
                  disabled={isSubmitting}
                />
              </div>

              {/* Action buttons */}
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={handleClose}
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleSubmit}
                  disabled={isSubmitting || !feedback.trim() || !points}
                  className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 disabled:opacity-50"
                >
                  {isSubmitting ? 'Updating...' : 'Update Feedback'}
                </button>
              </div>
            </div>

            {/* Close button */}
            <button
              className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              <Image
                src="/assets/images/all-img/cross-bg.png"
                alt="Close"
                width={40}
                height={40}
                className="w-full h-auto"
                priority
              />
            </button>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default EssayFeedbackModal;
