'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { toast } from 'sonner';
import api from '@/lib/api';
import useDataFetch from '@/hooks/useDataFetch';
import HecSubmissionLayout from '@/components/layouts/HecSubmissionLayout';
import { Icon } from '@iconify/react';
import { formatDate } from '@/utils/dateFormatter';
import DiaryReviewSection from '../../../hec-diary/review/_components/DiaryReviewSection';
import DiaryCanvas from '../../../hec-diary/review/_components/DiaryCanvas';
import EssayReviewSection from '../_components/EssayReviewSection';
import SkinPreview from '@/components/skin/SkinPreview';

const EssayReviewPage = () => {
  const { id } = useParams();
  const router = useRouter();
  const [correctionText, setCorrection] = useState('');
  const [feedback, setFeedback] = useState('');
  const [score, setScore] = useState('');
  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');
  const [activeTab, setActiveTab] = useState(tabParam || 'essaySubmissions');

  useEffect(() => {
    if (tabParam) {
      setActiveTab(tabParam);
    }
  }, [tabParam]);

  // Define tabs for the layout
  const tabs = [
    { name: 'Essay Submissions', value: 'essaySubmissions' },
    { name: 'Mission Essay', value: 'missionEssay' },
  ];

  // Fetch essay entry data
  const { data, isLoading, error } = useDataFetch({
    queryKey: 'essay-entry-review',
    endPoint: `/tutor-essay/${id}`,
    enabled: !!id,
  });

  // Initialize correction text with original content when data is loaded
  useEffect(() => {
    if (data?.content) {
      setCorrection(data.content);
    }
  }, [data?.content]);

  // Submit review function
  const submitReview = async () => {
    if (!score) {
      toast.error('Please provide a score');
      return;
    }

    if (!feedback.trim()) {
      toast.error('Please provide feedback');
      return;
    }

    try {
      const response = await api.post(`/tutor/essay/entries/${id}/correction`, {
        correctionText,
        feedback,
        score: parseInt(score),
      });

      if (response.success) {
        toast.success('Review submitted successfully');
        router.push(
          '/dashboard/submission-management/hec-essay?tab=essaySubmissions'
        );
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      toast.error(err.message || 'Failed to submit review');
    }
  };

  if (isLoading) {
    return (
      <HecSubmissionLayout
        activeTab={activeTab}
        tabs={tabs}
        title="HEC Essay"
        basePath="/dashboard/submission-management/hec-essay"
      >
        <div className="flex items-center justify-center h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
        </div>
      </HecSubmissionLayout>
    );
  }

  if (error) {
    return (
      <HecSubmissionLayout
        activeTab={activeTab}
        tabs={tabs}
        title="HEC Essay"
        basePath="/dashboard/submission-management/hec-essay"
      >
        <div className="bg-red-100 text-red-700 p-4 rounded-md">
          Error loading essay entry: {error.message}
        </div>
      </HecSubmissionLayout>
    );
  }

  return (
    <HecSubmissionLayout
      activeTab={activeTab}
      tabs={tabs}
      title="HEC Essay"
      basePath="/dashboard/submission-management/hec-essay"
    >
      <div className="flex justify-between items-center mb-2">
        <div className="flex gap-4 items-center">
          <h6 className="text-sm text-black font-medium ">Submitted by:</h6>
          <h2 className="text-[#464646] font-normal">
            {data?.diary?.userName}
          </h2>
        </div>
        <div className="flex items-center gap-3 text-sm text-gray-500">
          <div className="font-medium text-sm text-black ">Date:</div>
          <div className="flex items-center gap-3">
            <Icon
              icon="uil:calender"
              width="24"
              height="24"
              className="mx-auto text-gray-900 p-1 rounded-full bg-[#FFF189]"
            />
            {formatDate(data.entryDate, 'ordinal')}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 items-center bg-[#FDE7E9] gap-2 p-1 shadow-xl">
        {/* Diary Canvas */}
        <div className="bg-white h-full flex items-center justify-center p-2 overflow-hidden shadow-xl">
          <div className="w-full h-[500px] flex items-center justify-center overflow-hidden">
            <div
              className="canvas-container-wrapper"
              style={{ width: '100%', height: '100%', padding: '20px' }}
            >
              {/* <DiaryCanvas data={data?.diarySkin} /> */}
              <SkinPreview
                skin={data?.diarySkin?.templateContent}
                contentData={{
                  subject: data?.title,
                  body: data?.submissionHistory[
                    data?.submissionHistory.length - 1
                  ]?.content,
                  date: data?.submissionHistory[
                    data?.submissionHistory.length - 1
                  ]?.submissionDate?.slice(0, 10),
                }}
              />
            </div>
          </div>
        </div>

        {/* Review Section */}
        <EssayReviewSection data={data} entryId={id} />
      </div>
    </HecSubmissionLayout>
  );
};

export default EssayReviewPage;
