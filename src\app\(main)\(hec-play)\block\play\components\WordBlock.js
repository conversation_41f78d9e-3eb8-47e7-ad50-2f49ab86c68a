import React from 'react';
import Image from 'next/image';

const WordBlock = ({
  words = [],
  usedWords = [],
  title,
  hide = false,
  disable = false,
  disableDrag = false,
  disableClick = false,
  dragType,
  onDragStart,
  onClick,
  showImage = true,
  imageSrc = "/assets/images/all-img/footer_butterfly.png",
  imageWidth = 50,
  imageHeight = 50,
  imageAlt = "Icon",
  containerClassName = "",
  wordsContainerClassName = "",
  wordClassName = "",
  style = {}
}) => {
  if (hide) return null;

  const handleDragStart = (word) => (e) => {
    if (disable || disableDrag) {
      e.preventDefault();
      return;
    }
    if (onDragStart) {
      onDragStart(word, dragType);
    }
  };

  const handleClick = (word, index) => (e) => {
    if (disable || disableClick) {
      e.preventDefault();
      return;
    }
    if (onClick) {
      onClick(word, index);
    }
  };

  const getWordClassName = () => {
    let baseClass = "px-4 py-2 bg-[#FFF8E6] border border-orange-500 rounded-md text-sm";

    if (disable) {
      baseClass += " opacity-50 cursor-not-allowed";
    } else if (!disableDrag && !disableClick) {
      baseClass += " cursor-move shadow";
    } else if (!disableClick) {
      baseClass += " cursor-pointer";
    }

    if (wordClassName) {
      baseClass += ` ${wordClassName}`;
    }

    return baseClass;
  };

  const getContainerClassName = () => {
    let baseClass = "relative bg-white rounded-[32px] border-4 border-orange-300 p-6";

    if (containerClassName) {
      baseClass += ` ${containerClassName}`;
    }

    return baseClass;
  };

  const getWordsContainerClassName = () => {
    let baseClass = "flex flex-wrap gap-3";

    if (wordsContainerClassName) {
      baseClass += ` ${wordsContainerClassName}`;
    }

    return baseClass;
  };

  return (
    <div className={getContainerClassName()} style={style}>
      {showImage && (
        <Image
          src={imageSrc}
          width={imageWidth}
          height={imageHeight}
          alt={imageAlt}
          className="absolute -left-8 -top-8"
        />
      )}
      {title && <h2 className="text-lg font-bold mb-4">{title}</h2>}
      <div className={getWordsContainerClassName()}>
        {words.map((word, i) =>
          !usedWords.includes(word) ? (
            <div
              key={i}
              draggable={!disable && !disableDrag}
              onDragStart={handleDragStart(word)}
              onClick={handleClick(word, i)}
              className={getWordClassName()}
            >
              {word}
            </div>
          ) : null
        )}
      </div>
    </div>
  );
};

export default WordBlock;
