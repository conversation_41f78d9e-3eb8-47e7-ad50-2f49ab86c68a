'use client';
import React, { useEffect, useState } from 'react';
import api from '@/lib/api';
import SentenceBlock from './components/SentenceBlock';
import GameInstructionView from './components/GameInstructionView';



const BlockSentenceBuilder = () => {
  const [gameData, setGameData] = useState(null);
  const [startingSentences, setStartingSentences] = useState([]);
  const [expandingSentences, setExpandingSentences] = useState([]);
  const [draggedWord, setDraggedWord] = useState(null);
  const [usedWords, setUsedWords] = useState([]);
  const [currentStep, setCurrentStep] = useState(0);
  const [submitted, setSubmitted] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await api.get('/play/block/play');
        const sentences = res.data.sentences;
        const start = sentences.map((s) =>
          Array(s.starting_part.split(' ').length).fill(null)
        );
        const expand = sentences.map((s) =>
          Array(s.expanding_part.split(' ').length).fill(null)
        );
        setGameData(res.data);
        setStartingSentences(start);
        setExpandingSentences(expand);
      } catch (err) {
        console.error('Error fetching block data:', err);
      }
    };
    fetchData();
  }, []);

  const handleDragStart = (word, type) => {
    setDraggedWord({ word, type });
  };

  const handleDrop = (type, index, blockIndex) => {
    if (!draggedWord || draggedWord.type !== type) return;
    if (
      type === 'expand' &&
      startingSentences[blockIndex].some((w) => w === null)
    )
      return;
    const sentences =
      type === 'start' ? [...startingSentences] : [...expandingSentences];
    const current = sentences[blockIndex];
    if (!current[index]) {
      current[index] = draggedWord.word;
      if (type === 'start') setStartingSentences(sentences);
      else setExpandingSentences(sentences);
      setUsedWords((prev) => [...prev, draggedWord.word]);
      setDraggedWord(null);
    }
  };

  const removeWord = (index, type, blockIndex) => {
    const sentences =
      type === 'start' ? [...startingSentences] : [...expandingSentences];
    const current = sentences[blockIndex];
    const wordToRemove = current[index];
    current[index] = null;
    if (type === 'start') setStartingSentences(sentences);
    else setExpandingSentences(sentences);
    setUsedWords((prev) => prev.filter((w) => w !== wordToRemove));
  };

  const allComplete = startingSentences.every(
    (s, i) =>
      s.every((w) => w !== null) &&
      expandingSentences[i].every((w) => w !== null)
  );

  const submitAll = async () => {
    try {
      // Prepare sentence constructions from the completed sentences
      const sentenceConstructions = gameData.sentences.map((_, index) => {
        const startingSentence = startingSentences[index]?.filter(word => word !== null).join(' ') || '';
        const expandingSentence = expandingSentences[index]?.filter(word => word !== null).join(' ') || '';

        return {
          starting_sentence: startingSentence,
          expanding_sentence: expandingSentence
        };
      });

      const payload = {
        block_game_id: gameData.id,
        sentence_constructions: sentenceConstructions
      };

      console.log('Submitting payload:', payload);

      const response = await api.post('/play/block/submit', payload);
      console.log('Game submitted successfully:', response.data);

      setSubmitted(true);

      // You can add success feedback here
      alert('Game submitted successfully!');

    } catch (error) {
      console.error('Error submitting game:', error);
      alert('Failed to submit game. Please try again.');
    }
  };

  if (!gameData) return <div className="p-6 text-center">Loading...</div>;

  return (
    <div className="min-h-screen py-10 px-4 flex flex-col items-center text-[#5A3D1A] space-y-12">
      <GameInstructionView
        gameData={gameData}
      />
      <SentenceBlock
        type="start"
        show={!submitted && currentStep < gameData.sentences.length}
        hide={submitted || currentStep >= gameData.sentences.length}
        currentStep={currentStep}
        gameData={gameData}
        startingSentences={startingSentences}
        expandingSentences={expandingSentences}
        usedWords={usedWords}
        handleDragStart={handleDragStart}
        handleDrop={handleDrop}
        removeWord={removeWord}
        onNext={() => setCurrentStep((prev) => prev + 1)}
      />

      <SentenceBlock
        type="expand"
        show={!submitted && currentStep >= gameData.sentences.length}
        hide={submitted || currentStep < gameData.sentences.length}
        currentStep={currentStep}
        gameData={gameData}
        startingSentences={startingSentences}
        expandingSentences={expandingSentences}
        usedWords={usedWords}
        handleDragStart={handleDragStart}
        handleDrop={handleDrop}
        removeWord={removeWord}
        allComplete={allComplete}
        onSubmit={submitAll}
        showCompletedSentences={true}
      />
    </div>
  );
};

export default BlockSentenceBuilder;
