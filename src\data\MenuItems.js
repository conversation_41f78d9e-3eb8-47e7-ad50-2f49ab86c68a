export const adminSidebarData = [
  {
    title: 'Dashboard',
    path: '/dashboard',
    icon: 'lucide:home',
  },

  {
    title: 'Member Management',
    path: '/dashboard/member-management',
    icon: 'fluent:person-circle-24-regular',
    children: [
      {
        title: 'Manage Request',
        path: '/dashboard/member-management/manage-request',
        icon: 'fluent:add-circle-24-regular',

      },

      {
        title: 'Approved Tutors',
        path: '/dashboard/member-management/approval-tutors',
        icon: 'fluent:add-circle-24-regular',

      },

      {
        title: 'Add Admin',
        path: '/dashboard/member-management/add-admin',
        icon: 'fluent:add-circle-24-regular',

      },

      {
        title: 'Manage Admins',
        path: '/dashboard/member-management/manage-admins',
        icon: 'fluent:add-circle-24-regular',

      },

      {
        title: 'Add Tutor',
        path: '/dashboard/member-management/add-tutor',
        icon: 'fluent:add-circle-24-regular',

      },

      {
        title: 'Tutor List',
        path: '/dashboard/member-management/tutor-list',
        icon: 'fluent:add-circle-24-regular',

      },

      {
        title: 'Add Student',
        path: '/dashboard/member-management/add-student',
        icon: 'fluent:add-circle-24-regular',

      },

      {
        title: 'Student List',
        path: '/dashboard/member-management/student-list',
        icon: 'fluent:add-circle-24-regular',

      },
    ],
  },

  {
    title: 'Payment Management',
    path: '/dashboard/payments',
    icon: 'fluent:payment-16-regular',
  },
  {
    title: 'Event Management',
    path: '/dashboard/events',
    icon: 'mdi:event-note-outline',
    children: [
      {
        title: "Today's Diary",
        path: '/admin/courses',
      },
      {
        title: 'Full Diary',
        path: '/admin/courses/create',
      },
      {
        title: 'Mission Diary',
        path: '/admin/courses/create',
      },
      {
        title: 'Awards',
        path: '/admin/courses/create',
      },
      {
        title: 'Owned Items',
        path: '/admin/courses/create',
      },
      {
        title: 'Shared Diary',
        path: '/admin/courses/create',
      },
      {
        title: 'Attendence Management',
        path: '/admin/courses/create',
      },
      {
        title: 'Success Rate Management',
        path: '/admin/courses/create',
      },
      {
        title: 'Milestone',
        path: '/admin/courses/create',
      },
    ],
  },
  {
    title: 'Emoticon Management',
    path: '/dashboard/emoticons',
    icon: 'tabler:icons',
    children: [
      {
        title: 'Manage Emoticons',
        path: '/dashboard/emoticons',
      },
      {
        title: 'Add Emoticons',
        path: '/dashboard/emoticons/add',
      },
    ],
  },
  {
    title: 'Skin Management',
    path: '/dashboard/skins',
    icon: 'icon-park-outline:category-management',
  },
  {
    title: 'Shop Management',
    path: '/dashboard/shops',
    icon: 'uil:shop',
    children: [
      {
        title: 'Manage Shop Category',
        path: '/dashboard/shops/categories',
      },
      {
        title: 'Add Shop Category',
        path: '/dashboard/shops/categories/add',
      },
      {
        title: 'Manage Items',
        path: '/dashboard/shops',
      },
      {
        title: 'Add Item',
        path: '/dashboard/shops/add',
      },
      {
        title: 'Promotion Applications',
        path: '/dashboard/shops/promotions',
      },
    ],
  },
  {
    title: 'Promotion Management',
    path: '/dashboard/promotions',
    icon: 'carbon:emissions-management',
    children: [
      {
        title: 'Manage Promotions',
        path: '/dashboard/promotions',
      },
      {
        title: 'Add Promotion',
        path: '/dashboard/promotions/add',
      },
    ],
  },

  {
    title: 'Module Management',
    path: '/dashboard/module-management',
    icon: 'carbon:chart-custom',
    children: [
      {
        title: 'HEC Diary',
        path: '/dashboard/module-management/hec-diary',
      },
      {
        title: 'HEC Play',
        path: '/dashboard/module-management/hec-play',
      },
      {
        title: 'HEC Q & A',
        path: '/dashboard/module-management/hec-qa',
      },
      {
        title: 'HEC Essay',
        path: '/dashboard/module-management/hec-essay',
      },
      {
        title: 'HEC Novel',
        path: '/dashboard/module-management/hec-novel',
      },
    ],
  },
  {
    title: 'Award Management',
    path: '/dashboard/awards',
    icon: 'carbon:trophy',
    children: [
      {
        title: 'Manage Award',
        path: '/dashboard/awards',
      },
      {
        title: 'Add Award',
        path: '/dashboard/awards/add',
      },
    ],
  },
  {
    title: 'Subscription Plans',
    path: '/dashboard/plans',
    icon: 'mdi:credit-card-outline',
    children: [
      {
        title: 'Manage Plans',
        path: '/dashboard/plans',
      },
      {
        title: 'Add Plan',
        path: '/dashboard/plans/add',
      },
      {
        title: 'Manage Plan Features',
        path: '/dashboard/plan-features',
      },
      {
        title: 'Add Plan Feature',
        path: '/dashboard/plan-features/add',
      },
    ],
  },
  // {
  //   title: 'Settings',
  //   path: '/dashboard/settings',
  //   icon: 'material-symbols:settings-outline-rounded',
  // },
];

export const tutorSidebarData = [
  {
    title: 'Dashboard',
    path: '/dashboard',
    icon: 'lucide:home',
  },
  {
    title: 'Submission Management',
    path: '/dashboard/submission-management',
    icon: 'mdi:clipboard-text-outline',
    children: [
      {
        title: 'HEC Diary',
        path: '/dashboard/submission-management/hec-diary',
      },
      // {
      //   title: 'HEC Play',
      //   path: '/dashboard/submission-management/hec-play',
      // },
      {
        title: 'HEC Q & A',
        path: '/dashboard/submission-management/hec-qa',
      },
      {
        title: 'HEC Essay',
        path: '/dashboard/submission-management/hec-essay',
      },
      {
        title: 'HEC Novel',
        path: '/dashboard/submission-management/hec-novel',
      },
    ],
  },
  {
    title: 'My Students',
    path: '/dashboard/students',
    icon: 'mingcute:user-4-line',
    children: [
      {
        title: 'Manage Students',
        path: '/dashboard/students',
      },
      {
        title: 'Add Student',
        path: '/dashboard/students/add',
      },
    ],
  },
  {
    title: 'Payment Management',
    path: '/dashboard/payments',
    icon: 'fluent:payment-16-regular',
  },
  // {
  //   title: 'Event Management',
  //   path: '/dashboard/events',
  //   icon: 'mdi:event-note-outline',
  //   children: [
  //     {
  //       title: "Today's Diary",
  //       path: '/admin/courses',
  //     },
  //     {
  //       title: 'Full Diary',
  //       path: '/admin/courses/create',
  //     },
  //     {
  //       title: 'Mission Diary',
  //       path: '/admin/courses/create',
  //     },
  //     {
  //       title: 'Awards',
  //       path: '/admin/courses/create',
  //     },
  //     {
  //       title: 'Owned Items',
  //       path: '/admin/courses/create',
  //     },
  //     {
  //       title: 'Shared Diary',
  //       path: '/admin/courses/create',
  //     },
  //     {
  //       title: 'Attendence Management',
  //       path: '/admin/courses/create',
  //     },
  //     {
  //       title: 'Success Rate Management',
  //       path: '/admin/courses/create',
  //     },
  //     {
  //       title: 'Milestone',
  //       path: '/admin/courses/create',
  //     },
  //   ],
  // },
  {
    title: 'Skin Management',
    path: '/dashboard/skins',
    icon: 'icon-park-outline:category-management',
  },
  // {
  //   title: 'Shop Management',
  //   path: '/dashboard/shops',
  //   icon: 'uil:shop',
  //   children: [
  //     {
  //       title: 'Manage Item Category',
  //       path: '/dashboard/shops/categories',
  //     },
  //     {
  //       title: 'Add Shop Category',
  //       path: '/dashboard/shops/add-shop-category',
  //     },
  //     {
  //       title: 'Manage Item',
  //       path: '/dashboard/shops',
  //     },
  //     {
  //       title: 'Add Item',
  //       path: '/dashboard/shops/add-shop',
  //     },
  //   ],
  // },
  {
    title: 'Module Management',
    path: '/dashboard/module-management',
    icon: 'carbon:chart-custom',
    children: [
      {
        title: 'HEC Q & A',
        path: '/dashboard/module-management/hec-qa',
      },
    ],
  },
  {
    title: 'Promotion Management',
    path: '/dashboard/promotions',
    icon: 'carbon:emissions-management',
  },
  // {
  //   title: 'Settings',
  //   path: '/dashboard/settings',
  //   icon: 'material-symbols:settings-outline-rounded',
  // },
];

export const userSidebarData = [
  {
    title: 'Dashboard',
    path: '/dashboard',
    icon: 'lucide:layout-dashboard',
  },
  {
    title: 'User',
    // path: '/dashboard/user',
    icon: 'lucide:user',
    children: [
      {
        title: 'All Users',
        path: '/dashboard/users',
      },
      // {
      //   title: 'Add User',
      //   path: '/dashboard/user/account',
      // },
    ],
  },
  // {
  //   title: 'HEC Diary',
  //   path: '/dashboard/diary',
  //   icon: 'lucide:book',
  //   children: [], // Add submenu items if needed
  // },
  // {
  //   title: 'HEC Q & A',
  //   path: '/dashboard/qa',
  //   icon: 'lucide:help-circle',
  //   children: [],
  // },
  // {
  //   title: 'HEC Essay',
  //   path: '/dashboard/essay',
  //   icon: 'lucide:file-text',
  //   children: [],
  // },
  // {
  //   title: 'HEC Novel',
  //   path: '/dashboard/novel',
  //   icon: 'lucide:book-open',
  //   children: [],
  // },
  // {
  //   title: 'Settings',
  //   path: '/dashboard/settings',
  //   icon: 'lucide:settings',
  //   children: [],
  // },
];
