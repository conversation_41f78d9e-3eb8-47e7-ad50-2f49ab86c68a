'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import ContentEditable from 'react-contenteditable';
import api from '@/lib/api';
import { formatDate } from '@/utils/dateFormatter';
import FeedbackModal from './FeedbackModal';

const isHtmlEmpty = (html) => {
  if (!html) return true;
  const strippedHtml = html.replace(/<[^>]*>?/gm, '').trim();
  return strippedHtml.length === 0;
};

const NovelReviewSection = ({ data, entryId }) => {
  const router = useRouter();
  const [correctionHtml, setCorrectionHtml] = useState(data?.content || '');
  const [score, setScore] = useState('');
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);
  const [isSubmittingReview, setIsSubmittingReview] = useState(false);
  const [isCompletingReview, setIsCompletingReview] = useState(false);
  const contentEditableRef = useRef(null);
  const isCorrectionReviewed = !!data?.correction?.correctionText;
  const hasExistingScore = !!data?.correction?.score;

  useEffect(() => {
    setCorrectionHtml(data?.content || '');
  }, [data?.content]);

  useEffect(() => {
    if (data?.correction?.score && !score) {
      setScore(data.correction.score.toString());
    }
  }, [data?.correction?.score, score]);

  const handleCorrectionChange = (evt) => {
    setCorrectionHtml(evt.target.value);
  };

  const prepareToTypeBlue = () => {
    document.execCommand('foreColor', false, 'blue');
  };

  // Fixed validation function
  const isSubmitDisabled = () => {
    const hasCorrection = !isHtmlEmpty(correctionHtml);
    const hasScore = score || data?.correction?.score;
    // Require at least correction text to submit
    return !hasCorrection || isSubmittingReview;
  };

  const submitReview = async () => {
    try {
      setIsSubmittingReview(true);
      
      // Strip HTML tags from correction content and ensure it's a plain string
      const plainTextCorrection = correctionHtml
        ? correctionHtml.replace(/<[^>]*>?/gm, '').trim()
        : '';
      
      // Check if this is an update (has existing score) or new review
      if (hasExistingScore) {
        // Update existing review - only send correction, NO score
        const payload = {
          correction: plainTextCorrection || 'Review updated'
        };
        
        console.log('Updating review with payload:', payload);
        
        const response = await api.put(`/tutor/novel/entries/${entryId}/correction`, payload);
        
        if (response.success) {
          router.push('/dashboard/submission-management/hec-novel');
        } else {
          throw new Error(response.message);
        }
      } else {
        // New review - send both correction and score
        const scoreToSubmit = score || data?.correction?.score;
        const payload = {
          correction: plainTextCorrection || 'Review completed'
        };
        
        // Only add score if it exists and is valid
        if (scoreToSubmit && !isNaN(parseInt(scoreToSubmit))) {
          payload.score = parseInt(scoreToSubmit);
        }
        
        console.log('Creating new review with payload:', payload);
        
        const response = await api.put(`/tutor/novel/entries/${entryId}/review`, payload);
        
        if (response.success) {
          router.push('/dashboard/submission-management/hec-novel');
        } else {
          throw new Error(response.message);
        }
      }
    } catch (err) {
      console.error('Submit error:', err);
      toast.error(err.message || 'Failed to submit review');
    } finally {
      setIsSubmittingReview(false);
    }
  };

  const completeReview = async () => {
    try {
      setIsCompletingReview(true);
      const response = await api.post(`/tutor/novel/entries/${entryId}/confirm`);
      if (response.success) {
        router.push('/dashboard/submission-management/hec-novel');
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      toast.error(err.message || 'Failed to complete review');
    } finally {
      setIsCompletingReview(false);
    }
  };

  return (
    <>
      <div className="p-2 shadow-xl h-full bg-white">
        <div className="mb-4 rounded-md shadow-lg p-4 h-[204px] overflow-y-auto">
          <div className="flex justify-between items-center border-b-2 border-dashed border-gray-300 mb-3">
            <h3 className="text-lg font-semibold mb-2">Original Content</h3>
            <div className="flex items-center gap-3 text-sm">
              Date: {formatDate(data.entryDate, 'ordinal')}
            </div>
          </div>
          <p className="whitespace-pre-wrap text-sm text-[#314158]">{data.content}</p>
        </div>

        <div className="h-[400px] overflow-auto shadow-lg p-4 rounded-md">
          <p className="text-sm text-[#864D0D] text-center font-medium">Tutor Review Zone</p>
          <div className="flex justify-between items-center border-b-2 border-dashed border-gray-300 mb-3">
            <h3 className="text-lg font-semibold mb-2">Make Corrections</h3>
            <div className="flex items-center gap-3 text-sm">
              Date: {formatDate(data.entryDate, 'ordinal')}
            </div>
          </div>

          <div className="mb-4">
            <ContentEditable
              innerRef={contentEditableRef}
              html={correctionHtml}
              disabled={false}
              onChange={handleCorrectionChange}
              onFocus={prepareToTypeBlue}
              onClick={prepareToTypeBlue}
              onKeyDown={(e) => {
                if (
                  e.key.length === 1 ||
                  e.key === 'Enter' ||
                  e.key === 'Backspace' ||
                  e.key === 'Delete'
                ) {
                  prepareToTypeBlue();
                }
              }}
              className="w-full p-3 border border-gray-300 rounded-lg min-h-[9rem] focus:outline-none focus:ring-2 focus:ring-yellow-300 editable-content"
              data-placeholder="Make corrections here..."
            />
            <style jsx global>{`
              .editable-content:empty::before {
                content: attr(data-placeholder);
                color: #a0aec0;
                pointer-events: none;
              }
            `}</style>
          </div>

         <div className="flex flex-wrap gap-3 sm:justify-between">
  {/* Left section: Feedback & Score */}
  <div className="flex flex-wrap gap-3">
    <button
      type="button"
      onClick={() => setIsFeedbackModalOpen(true)}
      className="px-3 py-1 bg-[#FEFCE8] text-xs sm:text-sm text-[#723F11] rounded-md border border-[#723F11] font-medium hover:bg-[#FFF8D6] min-w-[110px]"
    >
      Give feedback
    </button>

    <div className="flex items-center border border-[#723F11] rounded-md overflow-hidden text-xs sm:text-sm">
      <label className="px-3 py-1 bg-[#FEFCE8] text-[#723F11] font-medium whitespace-nowrap">
        Score
      </label>
      {isCorrectionReviewed ? (
        <div className="px-3 py-1 text-gray-700 bg-white">{data.correction.score}</div>
      ) : hasExistingScore ? (
        <div className="px-3 py-1 text-gray-700 bg-gray-100">{data.correction.score}</div>
      ) : (
        <input
          type="number"
          value={score}
          onChange={(e) => setScore(e.target.value)}
          className="w-16 px-2 py-1 focus:outline-none text-gray-700 text-center"
          min="0"
          max="100"
          placeholder="---"
        />
      )}
    </div>
  </div>

  {/* Right section: Confirm & Submit */}
  <div className="flex flex-wrap gap-3">
    <button
      type="button"
      onClick={completeReview}
      disabled={isCompletingReview}
      className="px-4 py-1 text-xs sm:text-sm bg-yellow-500 text-white rounded-md hover:bg-yellow-600 disabled:bg-gray-300 disabled:cursor-not-allowed min-w-[100px]"
    >
      {isCompletingReview ? 'Confirming...' : 'Confirm'}
    </button>

    <button
      type="button"
      onClick={submitReview}
      disabled={isSubmitDisabled()}
      className="px-4 py-1 text-xs sm:text-sm bg-yellow-500 text-white rounded-md hover:bg-yellow-600 disabled:bg-gray-300 disabled:cursor-not-allowed min-w-[120px]"
    >
      {isSubmittingReview 
        ? 'Submitting...' 
        : hasExistingScore 
          ? 'Update Review' 
          : 'Submit Review'
      }
    </button>
  </div>
</div>

        </div>
      </div>

      <FeedbackModal
        isOpen={isFeedbackModalOpen}
        onClose={() => setIsFeedbackModalOpen(false)}
        entryId={entryId}
        feedbacks={data?.feedbacks || []}
        refetch={() => {
          // Add refetch logic here if needed
          console.log('Refetching data...');
        }}
      />
    </>
  );
};

export default NovelReviewSection;