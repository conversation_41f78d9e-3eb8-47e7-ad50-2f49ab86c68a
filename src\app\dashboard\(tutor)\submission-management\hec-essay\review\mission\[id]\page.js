'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { toast } from 'sonner';
import api from '@/lib/api';
import useDataFetch from '@/hooks/useDataFetch';
import HecSubmissionLayout from '@/components/layouts/HecSubmissionLayout';
import { Icon } from '@iconify/react';
import { formatDate } from '@/utils/dateFormatter';
import FeedbackModal from '../../../../hec-diary/review/_components/FeedbackModal';
import EditorViewer from '@/components/EditorViewer';
import { ButtonIcon } from '@/components/Button';

const MissionEssayReviewPage = () => {
  const { id } = useParams();
  const router = useRouter();
  const [correctionText, setCorrection] = useState('');
  const [feedback, setFeedback] = useState('');
  const [score, setScore] = useState('');
  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');
  const [activeTab, setActiveTab] = useState(tabParam || 'missionEssay');
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);

  useEffect(() => {
    if (tabParam) {
      setActiveTab(tabParam);
    }
  }, [tabParam]);

  // Define tabs for the layout
  const tabs = [
    { name: 'Essay Submissions', value: 'essaySubmissions' },
    { name: 'Mission Essay', value: 'missionEssay' },
  ];

  // Fetch mission essay entry data
  const { data, isLoading, error, refetch } = useDataFetch({
    queryKey: 'mission-essay-entry-review',
    endPoint: `/tutor-essay/missions/${id}`,
    enabled: !!id,
  });

  // Initialize correction text with original content when data is loaded
  useEffect(() => {
    if (data?.content) {
      setCorrection(data.content);
    }
  }, [data?.content]);

  // Submit review function
  const submitReview = async () => {
    if (!score) {
      toast.error('Please provide a score');
      return;
    }

    if (!feedback.trim()) {
      toast.error('Please provide feedback');
      return;
    }

    try {
      const response = await api.post(
        `/tutor/essay/mission/entries/${id}/correction`,
        {
          correctionText,
          feedback,
          score: parseInt(score),
        }
      );

      if (response.success) {
        toast.success('Review submitted successfully');
        router.push(
          '/dashboard/submission-management/hec-essay?tab=missionEssay'
        );
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      toast.error(err.message || 'Failed to submit review');
    }
  };

  if (isLoading) {
    return (
      <HecSubmissionLayout
        activeTab={activeTab}
        tabs={tabs}
        title="HEC Essay"
        basePath="/dashboard/submission-management/hec-essay"
      >
        <div className="flex items-center justify-center h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
        </div>
      </HecSubmissionLayout>
    );
  }

  if (error) {
    return (
      <HecSubmissionLayout
        activeTab={activeTab}
        tabs={tabs}
        title="HEC Essay"
        basePath="/dashboard/submission-management/hec-essay"
      >
        <div className="bg-red-100 text-red-700 p-4 rounded-md">
          Error loading mission essay entry: {error.message}
        </div>
      </HecSubmissionLayout>
    );
  }

  return (
    <HecSubmissionLayout
      activeTab={activeTab}
      tabs={tabs}
      title="HEC Essay"
      basePath="/dashboard/submission-management/hec-essay"
    >
      <div className="flex justify-between items-center mb-2">
        <div className="flex gap-4 items-center">
          <h6 className="text-lg text-gray-700 font-medium ">
            View Mission Essay
          </h6>
          <h2 className="text-[#464646] font-normal">
            {data?.diary?.userName}
          </h2>
        </div>
      </div>

      <div className="grid items-center bg-[#FFF9FB] gap-2 p-4 shadow-xl border rounded-lg space-y-3">
        {/* Diary Canvas */}
        <div className="p-5 bg-[#FCF8EF] rounded-lg [box-shadow:2px_2px_12px_0px_#F5D1B066_inset,_-2px_-2px_12px_0px_#F5D1B066_inset]  flex items-center justify-between">
          <div>
            <h1 className="text-2xl text-yellow-800 font-semibold">
              {data?.mission?.title}
            </h1>
            <p>Instructions:</p>
            <EditorViewer data={data?.mission?.description} />
          </div>

          <h2 className="text-3xl font-semibold text-yellow-600 font-serif">
            Mission Essay
          </h2>
        </div>

        {/* Original Content */}
        <div>
          <h3 className="text-xl text-yellow-800 font-semibold mb-2">
            Student Submission
          </h3>
          <div className="rounded-md shadow border p-4 min-h-32 max-h-72 overflow-y-auto bg-white">
            <EditorViewer
              data={data?.content}
              className="whitespace-pre-wrap text-sm text-[#314158]"
            />
          </div>
        </div>

        {/* Correction Section */}
        <div className="overflow-y-auto">
          <div className="flex justify-between items-center">
            <h3 className="text-xl text-yellow-800 font-semibold mb-2">
              Tutor Correction Zone
            </h3>
          </div>
          <textarea
            value={correctionText}
            onChange={(e) => setCorrection(e.target.value)}
            className="w-full min-h-[120px] p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500 text-sm"
          />

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-5">
              <ButtonIcon
                icon={'arcticons:feedback-2'}
                innerBtnCls={'h-12 w-12'}
                btnIconCls={'h-5 w-5'}
                onClick={() => setIsFeedbackModalOpen(true)}
              />
              <div className="flex items-center">
                <label className="mr-2">Score:</label>
                <input
                  type="number"
                  value={score}
                  onChange={(e) => setScore(e.target.value)}
                  className="w-24 border border-gray-300 rounded px-2 py-1"
                  min="0"
                  max="100"
                  placeholder="0-100"
                />
              </div>
            </div>
            <div className="flex items-center gap-4">
              <button
                onClick={() => handleBack()}
                className="px-4 py-2 bg-gray-400 text-white rounded-md"
              >
                Cancel
              </button>
              <button
                onClick={submitReview}
                disabled={!correctionText.trim() || !score}
                className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                Submit Review
              </button>
            </div>
          </div>
        </div>
      </div>

      <FeedbackModal
        isOpen={isFeedbackModalOpen}
        feedbacks={data?.feedbacks || []}
        onClose={() => setIsFeedbackModalOpen(false)}
        refetch={refetch}
        entryId={id}
      />
    </HecSubmissionLayout>
  );
};

export default MissionEssayReviewPage;
