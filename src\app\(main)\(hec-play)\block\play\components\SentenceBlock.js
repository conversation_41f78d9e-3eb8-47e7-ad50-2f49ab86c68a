import React from 'react';
import WordBlock from './WordBlock';
import DropZone from './DropZone';

const SentenceBlock = ({
  show = true,
  hide = false,
  type = 'start', // 'start' or 'expand'
  currentStep,
  gameData,
  startingSentences,
  expandingSentences,
  usedWords,
  handleDragStart,
  handleDrop,
  removeWord,
  onNext,
  onSubmit,
  allComplete = false,
  title,
  showCompletedSentences = false,
}) => {
  if (hide || !show || !gameData) return null;

  const isStartingType = type === 'start';
  const words = isStartingType
    ? gameData.word_blocks.starting_words
    : gameData.word_blocks.expanding_words;

  const sentences = isStartingType ? startingSentences : expandingSentences;
  const dragType = isStartingType ? 'start' : 'expand';

  const getTitle = () => {
    if (title) return title;
    return isStartingType
      ? `Starting Sentence Block set #${currentStep + 1}`
      : 'Expanding Sentence Block Set';
  };

  const renderWordBlocks = () => (
    <WordBlock
      words={words}
      usedWords={usedWords}
      title={getTitle()}
      hide={false}
      disable={false}
      disableDrag={false}
      disableClick={true}
      dragType={dragType}
      onDragStart={handleDragStart}
      showImage={true}
      imageSrc="/assets/images/all-img/footer_butterfly.png"
      imageWidth={50}
      imageHeight={50}
      imageAlt="Icon"
    />
  );

  const renderDropZones = () => {
    if (isStartingType) {
      return (
        <div className="bg-white rounded-[32px] border-4 border-orange-300 p-4 flex flex-wrap gap-3">
          {sentences[currentStep].map((word, i) => (
            <DropZone
              key={i}
              word={word}
              index={i}
              blockIndex={currentStep}
              hide={false}
              disable={false}
              disableDrop={false}
              disableRemove={false}
              dragType={dragType}
              onDrop={handleDrop}
              onRemoveWord={removeWord}
            />
          ))}
        </div>
      );
    } else {
      // Expanding sentences - show all sentences
      return gameData.sentences.map((_, blockIndex) => (
        <div key={blockIndex} className="w-full flex gap-4 items-center">
          <p className="mb-2 font-medium">
            {blockIndex + 1}. {startingSentences[blockIndex].join(' ')}
          </p>
          <div className="flex flex-wrap gap-3">
            {sentences[blockIndex].map((word, i) => (
              <DropZone
                key={i}
                word={word}
                index={i}
                blockIndex={blockIndex}
                hide={false}
                disable={false}
                disableDrop={false}
                disableRemove={false}
                dragType={dragType}
                onDrop={handleDrop}
                onRemoveWord={removeWord}
              />
            ))}
          </div>
        </div>
      ));
    }
  };

  const renderCompletedSentences = () => {
    return (
      <div className="w-full max-w-5xl bg-white border-4 border-orange-300 rounded-[32px] px-6 py-4">
        <h3 className="text-lg font-bold mb-4">Completed Sentences</h3>
        <div className="space-y-4">
          {startingSentences.map((s, i) => {
            const start = s.join(' ');
            const expand = expandingSentences[i].join(' ');
            return (
              <div key={i} className="flex flex-wrap gap-2 items-center">
                <span className="text-sm text-[#5A3D1A]">{start}</span>
                {expand.split(' ').map((w, idx) => (
                  <span
                    key={idx}
                    className="text-sm text-[#EA7D00] font-medium"
                  >
                    {w}
                  </span>
                ))}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderActionButton = () => {
    if (isStartingType && sentences[currentStep].every((w) => w !== null)) {
      return (
        <button
          onClick={onNext}
          className="px-6 py-2 bg-orange-400 hover:bg-orange-500 text-white rounded-lg font-semibold shadow-md"
        >
          Next
        </button>
      );
    } else if (!isStartingType && allComplete && onSubmit) {
      return (
        <button
          onClick={onSubmit}
          className="mt-4 px-6 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg font-semibold shadow-md"
        >
          Submit All Sentences
        </button>
      );
    }
    return null;
  };

  return (
    <div className="w-full max-w-5xl space-y-6">
      {renderWordBlocks()}
      {renderDropZones()}
      {renderCompletedSentences()}
      {renderActionButton()}
    </div>
  );
};

export default SentenceBlock;
