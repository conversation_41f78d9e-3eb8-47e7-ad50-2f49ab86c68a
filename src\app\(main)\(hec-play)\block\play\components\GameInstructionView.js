import React from 'react';
import WordBlock from './WordBlock';
import Image from 'next/image';

const GameInstructionView = ({ gameData }) => {
  if (!gameData) return null;

  // Extract data from gameData
  const startingWords = gameData?.word_blocks?.starting_words || [];
  const expandingWords = gameData?.word_blocks?.expanding_words || [];
  const title = 'Create Sentence Using The Words In The Block.';
  const instructions = [
    'Write a starting sentence from Block set # 1',
    'Expand that sentence using Block set # 2',
  ];
  const totalScore = 10;
  return (
    <div className="w-full max-w-5xl mx-auto relative bg-[#EDFDFD] p-4 mb-8">
      {/* Header with instructions on left and score image on right */}
      <div className="flex justify-between items-start">
        {/* Left side - Instructions */}
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-[#8B4513] mb-6">{title}</h1>
          <div className="text-[#5A3D1A] mb-4">
            <div className="font-semibold mb-2">Instruction :</div>
            <ol className="list-decimal list-inside space-y-1 ml-4">
              {instructions.map((instruction, index) => (
                <li key={index} className="text-sm">
                  {instruction}
                </li>
              ))}
            </ol>
          </div>
        </div>

        {/* Right side - Score card with background image */}
        <div
          className="w-48 h-24 bg-cover bg-center bg-no-repeat rounded-[20px] flex items-center justify-center ml-8"
          style={{
            backgroundImage: "url('/assets/Frame 1000008044.png')",
          }}
        >
          <div className="text-[#8B4513] font-bold text-lg">
            • Total Score: {totalScore}
          </div>
        </div>
      </div>

      {/* Word Blocks */}
      <div className="w-full space-y-8">
        {/* Starting Sentence Block */}
        {startingWords.length > 0 && (
          <WordBlock
            words={startingWords}
            usedWords={[]}
            title="Starting Sentence Block set # 1"
            readOnly={true}
            showImage={true}
            imageSrc="/assets/cat.png"
            imageWidth={80}
            imageHeight={80}
            imageAlt="Cat Icon"
            containerClassName="border-orange-300"
          />
        )}

        {/* Expanding Sentence Block */}
        {expandingWords.length > 0 && (
          <WordBlock
            words={expandingWords}
            usedWords={[]}
            title="Expanding Sentence Block set # 2"
            readOnly={true}
            showImage={true}
            imageSrc="/assets/tiger.png"
            imageWidth={80}
            imageHeight={80}
            imageAlt="Tiger Icon"
            containerClassName="border-orange-300"
          />
        )}
      </div>

      {/* Decorations */}
      <div className="w-full mt-8">
        <Image
          src="/assets/Frame 1000007108.png"
          alt="Decoration"
          width={500}
          height={20}
          className="w-full h-auto"
        />
      </div>
    </div>
  );
};

export default GameInstructionView;
