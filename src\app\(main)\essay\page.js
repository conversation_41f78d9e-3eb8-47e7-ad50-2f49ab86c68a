'use client';

import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'sonner';
import { useRouter, useSearchParams } from 'next/navigation';
import SkinPreview from '@/components/skin/SkinPreview';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import DetailsModal from '@/components/form/modal/MissionConfirmationModal';
import { ButtonIcon } from '@/components/Button';
import EssayFeedBackModal from './_components/FeedbackModal';
import Tooltip from '@/components/Tooltip';
import {
  selectIsSkinModalOpen,
  selectLayoutBackground,
  setIsSkinModalOpen,
} from '@/store/features/diarySlice';
import SelectSkinModal from '../diary/_component/SelectSkinModal';

export default function WriteEssay() {
  const router = useRouter();
  const dispatch = useDispatch();
  const taskId = useSearchParams().get('taskId');
  const [isSaving, setIsSaving] = useState(false);
  const [modalData, setModalData] = useState(null);
  const [showError, setShowError] = useState(false);
  const isSkinModalOpen = useSelector(selectIsSkinModalOpen);
  const [selectedSkin, setSelectedSkin] = useState(null);
  const layoutBackground = useSelector(selectLayoutBackground);
  const [subject, setSubject] = useState('');
  const [body, setBody] = useState('');
  const [wordCount, setWordCount] = useState(0); // Single declaration
  const [date, setDate] = useState(new Date().toISOString().slice(0, 10));

  if (!taskId) return router.push('/essay/mission');

  const { data: submissionDetails, refetch } = useDataFetch({
    queryKey: ['/student-essay/submissions', taskId],
    endPoint: `/student-essay/submissions/${taskId}`,
    enabled: !!taskId,
  });

  const isEditing = submissionDetails?.status !== 'submitted';

  const latestSubmission = submissionDetails?.submissionHistory?.at(-1) || {};

  const { data: skinInfo, isLoading } = useDataFetch({
    queryKey: ['essay-skin-info', taskId],
    endPoint: `/student-essay/skins/${taskId}`,
    enabled: !!taskId,
  });

  useEffect(() => {
    setSubject(submissionDetails?.title || ' ');
    setBody(latestSubmission?.content || '');
    setDate(
      latestSubmission?.submissionDate
        ? new Date(latestSubmission?.submissionDate).toISOString().slice(0, 10)
        : new Date().toISOString().slice(0, 10)
    );
  }, [submissionDetails]);

  useEffect(() => {
    if (!isLoading) {
      if (
        skinInfo?.taskSpecificSkin?.skin ||
        skinInfo?.moduleDefaultSkin?.skin
      ) {
        dispatch(setIsSkinModalOpen(false));
        setSelectedSkin(
          skinInfo?.taskSpecificSkin?.skin || skinInfo?.moduleDefaultSkin?.skin
        );
      } else {
        dispatch(setIsSkinModalOpen(true));
      }
    }
  }, [skinInfo]);

  const countWords = (html) => {
    if (!html) return 0;
    const text = html.replace(/<[^>]*>/g, ' ');
    const cleanText = text.replace(/&nbsp;|&|<|>|"|'/g, ' ');
    return cleanText
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0).length;
  };

  useEffect(() => {
    setWordCount(countWords(body));
  }, [body]);

  const handleSave = async () => {
    if (
      (submissionDetails?.task?.wordLimitMinimum
        ? wordCount < submissionDetails?.task?.wordLimitMinimum
        : wordCount < 0) ||
      (submissionDetails?.task?.wordLimitMaximum
        ? wordCount > submissionDetails?.task?.wordLimitMaximum
        : wordCount > Infinity)
    ) {
      setShowError(true);
      return;
    } else if (!selectedSkin?.id) {
      toast.error("Essay can't be submitted without a skin");
      return;
    } else {
      setShowError(false);
    }

    setIsSaving(true);

    const payload = {
      taskId: taskId,
      // skinId: essayId,
      skinId: selectedSkin?.id || null,
      // backgroundColor: layoutBackground,
      title: subject,
      content: body,
    };
    console.log(payload);

    try {
      const response = await api.post('/student-essay/submit/task', payload);
      refetch();
      setShowError(false);
      router.push('/essay/mission');
    } catch (error) {
      console.error('Error saving:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleSkinChange = async (
    newSkin,
    dispatch,
    setSelectedSkin,
    setSubject,
    setMessage
  ) => {
    if (!newSkin.templateContent) {
      toast.error('This skin has no valid template content');
      return;
    }

    try {
      // 1. First reset everything
      // setSubject('');
      // setMessage('');

      // // 3. Parse and apply template
      // const templateData = JSON.parse(newSkin.templateContent);
      setSelectedSkin(newSkin);

      toast.success(`Skin "${newSkin.name}" applied successfully`);
    } catch (error) {
      console.error('Error applying skin template:', error);
      toast.error('Failed to apply skin template');
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-5 xl:px-0 my-8 relative">
      <div className="p-2 bg-[#FDE7E9] grid grid-cols-1 lg:grid-cols-2 gap-3 rounded">
        <div className="bg-white rounded-lg text-center flex items-center relative py-4">
          <div className="space-y-5">
            <SkinPreview
              skin={
                selectedSkin
                  ? selectedSkin?.templateContent
                  : skinInfo?.moduleDefaultSkin?.skin?.templateContent
              }
              contentData={{
                subject,
                body,
                date,
              }}
            />

            {submissionDetails?.status !== 'submitted' && (
              <button
                onClick={handleSave}
                disabled={isSaving}
                className={`  text-black font-medium py-2 px-8  text-center rounded-full whitespace-nowrap
                border-2 border-yellow-100
                shadow-[0px_4px_6px_-1px_rgba(0,0,0,0.1),0px_2px_4px_-2px_rgba(0,0,0,0.1),-4px_8px_12px_0px_#00000026]
                transition-all duration-300
                bg-gradient-to-b from-yellow-300 to-yellow-500 hover:from-yellow-400 hover:to-yellow-600
                relative pr-8
                ring-2 ring-[#A36105] ${
                  isSaving
                    ? 'bg-gray-300 cursor-not-allowed'
                    : 'bg-yellow-400 hover:bg-yellow-300'
                }`}
              >
                {isSaving ? 'Saving...' : 'Submit'}
              </button>
            )}
          </div>
        </div>

        {/* Example inputs for subject, body, and date */}
        <div className=" bg-white rounded-lg p-3 space-y-3 w-full">
          <div className="flex flex-col p-2 shadow-lg border rounded-lg gap-3">
            <div className="flex items-center justify-between border-b border-dashed pb-1 gap-3">
              {isEditing ? (
                <input
                  type="text"
                  value={subject}
                  onChange={(e) => setSubject(e.target.value)}
                  placeholder="Enter essay title..."
                  className="p-2 mr-2 w-full rounded border border-gray-200 focus:outline-[1px] focus:outline-gray-200"
                />
              ) : (
                <h3 className="p-2 mr-2 w-full">{subject}</h3>
              )}
              <span className="min-w-24">{date}</span>
            </div>
            {isEditing ? (
              <textarea
                value={body}
                onChange={(e) => setBody(e.target.value)}
                placeholder="Essay description"
                className={`border ${
                  latestSubmission?.submissionMark ? 'h-80' : 'min-h-60'
                } ${
                  showError && 'border-red-500'
                } p-2 w-full rounded focus:outline-[1px] focus:outline-gray-400 shadow-[inset_2px_2px_6px_0px_#0000001F]`}
                rows={4}
              />
            ) : (
              <p
                className={`border p-2 w-full rounded ${
                  latestSubmission?.submissionMark ? 'h-80' : 'min-h-60'
                } shadow-[inset_2px_2px_6px_0px_#0000001F]`}
              >
                {body || 'No description available.'}
              </p>
            )}

            {isEditing && (
              <div className="text-sm flex items-center justify-between">
                <span>{`${wordCount} / ${
                  submissionDetails?.task?.wordLimitMaximum || Infinity
                } (word)`}</span>

                {showError &&
                  wordCount < submissionDetails?.task?.wordLimitMinimum && (
                    <span className="text-red-500">
                      Minimum {submissionDetails?.task?.wordLimitMinimum} words
                      required.
                    </span>
                  )}
              </div>
            )}
          </div>

          {/* {latestSubmission?.submissionMark && ( */}
            <div className="min-h-40 max-h-72 overflow-y-auto shadow-lg border rounded-lg p-2 relative">
              <div className="h-full">
                <p className="text-sm text-[#864D0D] text-center font-medium mb-2">
                  Tutor Review Zone
                </p>
                <div className="flex justify-between items-center border-b-2 border-dashed border-gray-300 mb-3">
                  <h3 className="mb-2">{subject}</h3>
                  <div className="flex items-center gap-3 text-sm">{date}</div>
                </div>
                <div
                  className="min-h-28"
                  dangerouslySetInnerHTML={{
                    __html:
                      latestSubmission?.submissionMark ? latestSubmission?.submissionMark?.submissionFeedback : 'No feedback provided yet.',
                  }}
                />
                {/* {todayEntry?.status === 'confirm' && (
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                  <h1 className="text-lg text-[#14AE5C] font-medium">
                    Confirmed
                  </h1>
                </div>
              )} */}
              </div>

              <span className="absolute right-2 bottom-2">
                <ButtonIcon
                  icon="tabler:message-2-star"
                  innerBtnCls={`h-12 w-12`}
                  btnIconCls={`h-5 w-5`}
                  onClick={() => setModalData(body ? body : " ")}
                  aria-label=""
                />
              </span>
            </div>
          {/* )} */}
        </div>
      </div>

      <EssayFeedBackModal
        isOpen={!!modalData}
        onClose={() => setModalData(null)}
        data={''}
      />

      {isEditing && (
        <>
          <div className="absolute top-5 -right-10">
            <div className="w-8 h-8 cursor-pointer">
              <Tooltip
                content={'Skin'}
                color="user"
                size="lg"
                delay={100}
                className="-ml-3 "
                position="right"
              >
                <ButtonIcon
                  icon={'arcticons:image-combiner'}
                  innerBtnCls="h-12 w-12"
                  btnIconCls="h-5 w-5"
                  aria-label={'Skin'}
                  onClick={() => dispatch(setIsSkinModalOpen(true))}
                />
              </Tooltip>
            </div>
          </div>

          <SelectSkinModal
            isOpen={isSkinModalOpen}
            onClose={() => dispatch(setIsSkinModalOpen(false))}
            onApply={(skin) =>
              handleSkinChange(
                skin,
                dispatch,
                (skin) => setSelectedSkin(skin),
                (content) => setSubject(content),
                (content) => setBody(content)
              )
            }
            currentSkinId={skinInfo?.moduleDefaultSkin?.skin?.id}
          />
        </>
      )}
    </div>
  );
}
