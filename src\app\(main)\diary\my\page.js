'use client';
import { useState, useMemo, useCallback } from 'react';
import { ButtonIcon } from '@/components/Button';
import useDataFetch from '@/hooks/useDataFetch';
import DiaryCover from '../_component/DiaryCover';
import DiaryContent from '../_component/DiaryContent';
import DiaryPagination from '../_component/DiaryPagination';
import DiaryIconsSidebar from '../_component/DiaryIconsSidebar';

export default function MyDiary() {
  const [isOpen, setIsOpen] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [coverPhotoUrl, setCoverPhotoUrl] = useState(null);

  const { data, isLoading, error } = useDataFetch({
    queryKey: 'diary-entries',
    endPoint: 'diary/entries',
  });

  const entries = data?.items ?? [];
  const hasEntries = entries.length > 0;
  const isSinglePage = isOpen && currentIndex + 1 >= entries.length;

  const openDiary = useCallback(() => {
    if (hasEntries) {
      setCurrentIndex(0);
      setIsOpen(true);
    }
  }, [hasEntries]);

  const closeDiary = () => setIsOpen(false);

  const goLeft = () => setCurrentIndex((i) => Math.max(i - 2, 0));

  const goRight = () => {
    if (!isOpen && hasEntries) {
      openDiary();
      return;
    }
    setCurrentIndex((i) => (i + 2 < entries.length ? i + 2 : i));
  };

  const handleCoverPhotoChange = useCallback((url) => {
    setCoverPhotoUrl(url);
  }, []);

  const backgroundStyle = useMemo(
    () =>
      coverPhotoUrl
        ? {
            backgroundImage: `url(${coverPhotoUrl})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
          }
        : {},
    [coverPhotoUrl]
  );

  if (isLoading) {
    return (
      <div className="flex justify-center min-h-[calc(100vh-200px)] items-center">
        <p className="text-lg">Loading diary entries...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center min-h-[calc(100vh-200px)] items-center">
        <div className="text-center text-red-500">
          <p className="text-lg">Error loading diary entries</p>
          <p className="text-sm">{error.message}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex justify-center min-h-[calc(100vh-200px)]">
      <div
        className={`relative ${
          isOpen ? 'w-full max-w-[900px]' : 'w-full max-w-[647px]'
        } ${isSinglePage ? 'h-[858px]' : 'h-[658px]'} ${
          coverPhotoUrl ? '' : 'bg-[#FDE7E9]'
        } rounded-lg shadow-lg border border-gray-300 transition-all duration-300`}
        style={backgroundStyle}
      >
        {isOpen ? (
          <>
            <DiaryContent entries={entries} currentIndex={currentIndex} />
            <div className="absolute top-4 right-[-50px] z-10">
              <ButtonIcon
                icon="mdi:close"
                innerBtnCls="h-10 w-10 cursor-pointer"
                btnIconCls="h-5 w-5"
                aria-label="close diary"
                onClick={closeDiary}
              />
                            <DiaryIconsSidebar className="mt-20" showSkin={false} />

            </div>
          </>
        ) : (
          <DiaryCover
            hasEntries={hasEntries}
            onOpen={openDiary}
            onCoverPhotoChange={handleCoverPhotoChange}
          />
        )}
        <DiaryPagination
          hasEntries={hasEntries}
          isOpen={isOpen}
          currentIndex={currentIndex}
          totalEntries={entries.length}
          onLeftClick={goLeft}
          onRightClick={goRight}
        />
      </div>
    </div>
  );
}
