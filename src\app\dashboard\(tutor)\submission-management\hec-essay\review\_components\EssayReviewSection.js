'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import ContentEditable from 'react-contenteditable';
import api from '@/lib/api';
import { formatDate } from '@/utils/dateFormatter';
import { ButtonIcon } from '@/components/Button';
import Editor<PERSON>iewer from '@/components/EditorViewer';
import FeedbackModal from '../../../hec-diary/review/_components/FeedbackModal';

const isHtmlEmpty = (html) => {
  if (!html) return true;
  const strippedHtml = html.replace(/<[^>]*>?/gm, '').trim();
  return strippedHtml.length === 0;
};

const EssayReviewSection = ({ data, entryId }) => {
  const router = useRouter();
  const [correctionHtml, setCorrectionHtml] = useState(data?.content || '');
  const [score, setScore] = useState('');
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);
  const [isSubmittingReview, setIsSubmittingReview] = useState(false);
  const [isCompletingReview, setIsCompletingReview] = useState(false);
  const contentEditableRef = useRef(null);
  const isCorrectionReviewed = !!data?.correction?.correctionText;

  useEffect(() => {
    setCorrectionHtml(data?.content || '');
  }, [data?.content]);

  useEffect(() => {
    if (data?.correction?.score && !score) {
      setScore(data.correction.score.toString());
    }
  }, [data?.correction?.score, score]);

  const handleCorrectionChange = (evt) => {
    setCorrectionHtml(evt.target.value);
  };

  const prepareToTypeBlue = () => {
    document.execCommand('foreColor', false, 'blue');
  };

  const submitReview = async () => {
    try {
      setIsSubmittingReview(true);
      const scoreToSubmit = score || data?.correction?.score;
      const response = await api.post(`/tutor-essay/markEssay`, {
        submissionId: data?.id,
        points: score,
        submissionFeedback: correctionHtml,
      });
      if (response.success) {
        router.push('/dashboard/submission-management/hec-diary');
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      toast.error(err.message || 'Failed to submit review');
    } finally {
      setIsSubmittingReview(false);
    }
  };

  const handleGoBack = () => {
    router.push(
      '/dashboard/submission-management/hec-essay?tab=essaySubmissions'
    );
  };

  return (
    <>
      <div className="p-2 shadow-xl h-full bg-white space-y-5">
        <div className="rounded-md shadow-lg p-4 border min-h-52 overflow-y-auto">
          <div className="flex justify-between items-center border-b-2 border-dashed border-gray-300 mb-3">
            <h3 className="text-lg font-semibold mb-2">Original Content</h3>
            <div className="flex items-center gap-3 text-sm">
              Date: {formatDate(data.entryDate, 'ordinal')}
            </div>
          </div>
          <div className="max-h-60 overflow-y-auto">
            <EditorViewer
              data={
                data?.submissionHistory[data?.submissionHistory.length - 1]
                  ?.content
              }
            />
          </div>
        </div>

        <div className="border overflow-auto shadow-lg p-4 rounded-md">
          <p className="text-sm text-[#864D0D] text-center font-medium">
            Tutor Review Zone
          </p>
          <div className="flex justify-between items-center border-b-2 border-dashed border-gray-300 mb-3">
            <h3 className="text-lg font-semibold mb-2">Make Corrections</h3>
            <div className="flex items-center gap-3 text-sm">
              Date: {formatDate(data.entryDate, 'ordinal')}
            </div>
          </div>

          <div className="max-h-72 overflow-y-auto mb-3">
            <ContentEditable
              innerRef={contentEditableRef}
              html={correctionHtml}
              disabled={false}
              onChange={handleCorrectionChange}
              onFocus={prepareToTypeBlue}
              onClick={prepareToTypeBlue}
              onKeyDown={(e) => {
                if (
                  e.key.length === 1 ||
                  e.key === 'Enter' ||
                  e.key === 'Backspace' ||
                  e.key === 'Delete'
                ) {
                  prepareToTypeBlue();
                }
              }}
              className="w-full p-3 border border-gray-300 rounded-lg min-h-[9rem] focus:outline-none focus:ring-2 focus:ring-yellow-300 editable-content"
              data-placeholder="Make corrections here..."
            />
            <style jsx global>{`
              .editable-content:empty::before {
                content: attr(data-placeholder);
                color: #a0aec0;
                pointer-events: none;
              }
            `}</style>
          </div>

          <div className="flex items-center flex-wrap gap-3 sm:justify-between">
            {/* Left section: Feedback & Score */}
            <div className="flex items-center flex-wrap gap-3">
              <ButtonIcon
                icon={'arcticons:feedback-2'}
                innerBtnCls={'h-12 w-12'}
                btnIconCls={'h-5 w-5'}
                onClick={() => setIsFeedbackModalOpen(true)}
              />

              <div className="flex items-center border border-[#723F11] rounded-md overflow-hidden text-xs sm:text-sm">
                <label className="px-3 py-1 bg-[#FEFCE8] text-[#723F11] font-medium whitespace-nowrap">
                  Score
                </label>
                {isCorrectionReviewed ? (
                  <div className="px-3 py-1 text-gray-700 bg-white">
                    {data.correction.score}
                  </div>
                ) : (
                  <input
                    type="number"
                    value={score}
                    onChange={(e) => setScore(e.target.value)}
                    className="w-16 px-2 py-1 focus:outline-none text-gray-700 text-center"
                    min="0"
                    max="100"
                    placeholder="---"
                  />
                )}
              </div>
            </div>

            {/* Right section: Confirm & Submit */}
            <div className="flex flex-wrap gap-3">
              <button
                type="button"
                onClick={() => handleGoBack()}
                className="px-4 py-2 text-xs sm:text-sm bg-gray-400 text-white rounded-md min-w-[100px]"
              >
                Cancel
              </button>

              <button
                type="button"
                onClick={submitReview}
                disabled={
                  isHtmlEmpty(correctionHtml) ||
                  (!score && !data?.correction?.score) ||
                  isSubmittingReview
                }
                className="px-4 py-2 text-xs sm:text-sm bg-yellow-500 text-white rounded-md hover:bg-yellow-600 disabled:bg-gray-300 disabled:cursor-not-allowed min-w-[120px]"
              >
                {isSubmittingReview ? 'Submitting...' : 'Submit Review'}
              </button>
            </div>
          </div>
        </div>
      </div>

      <FeedbackModal
        isOpen={isFeedbackModalOpen}
        onClose={() => setIsFeedbackModalOpen(false)}
        onSubmit={async (feedback) => {
          const response = await api.post(
            `/tutor/diary/entries/${entryId}/feedback`,
            { feedback }
          );
          if (!response.success) {
            throw new Error(response.message || 'Failed to submit feedback');
          }
        }}
        title="Teachers Feedback"
        placeholder="Write here"
        submitButtonText="Confirm"
        submitButtonColor="bg-yellow-500 hover:bg-yellow-600"
      />
    </>
  );
};

export default EssayReviewSection;
