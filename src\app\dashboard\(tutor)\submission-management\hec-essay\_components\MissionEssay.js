'use client';

import React, { useState } from 'react';
import NewTablePage from '@/components/form/NewTablePage';
import { useRouter } from 'next/navigation';
import useDataFetch from '@/hooks/useDataFetch';

const MissionEssay = () => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [searchField, setSearchField] = useState('title');
  const [sortField, setSortField] = useState('createdAt'); // Default sort field
  const [sortDirection, setSortDirection] = useState('ASC');
  const [activeTab, setActiveTab] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [weekOrMonth, setWeekOrMonth] = useState('');

  const tabs = [
    { name: 'Weekly Mission', frequency: 'weekly' },
    { name: 'Monthly Mission', frequency: 'monthly' },
  ];

  const prepareParams = () => {
    const params = {
      page: currentPage,
      limit: rowsPerPage,
      timeFrequency: tabs[activeTab].frequency,
      sortDirection,
      sortBy: sortField, // Always use sortField instead of searchField
    };

    if (searchField === 'title' && searchTerm) {
      params.title = searchTerm;
    } else if (
      (searchField === 'week' || searchField === 'month') &&
      weekOrMonth
    ) {
      params.weekOrMonth = weekOrMonth;
    }

    return params;
  };

  const {
    data: missionData,
    isLoading: loading,
    error,
  } = useDataFetch({
    queryKey: [
      'tutor-essay-missions',
      activeTab,
      currentPage,
      rowsPerPage,
      sortField,
      sortDirection,
      searchTerm,
      searchField,
      weekOrMonth,
    ],
    endPoint: '/tutor-essay/missions',
    params: prepareParams(),
  });

  const columns =
    activeTab === 0
      ? [
          { label: 'WEEK', field: 'week' },
          { label: 'MISSION TITLE', field: 'title' },
          { label: 'WORD LIMIT', field: 'wordLimit' },
          { label: 'DEADLINE', field: 'deadline' },
        ]
      : [
          { label: 'MONTH', field: 'month' },
          { label: 'MISSION TITLE', field: 'title' },
          { label: 'WORD LIMIT', field: 'wordLimit' },
          { label: 'DEADLINE', field: 'deadline' },
        ];

  const actions = [
    {
      icon: 'heroicons-outline:eye',
      className:
        'text-blue-600 hover:text-blue-700 bg-blue-50 p-2 rounded-md cursor-pointer',
      onClick: (submission) =>
        router.push(
          `/dashboard/submission-management/hec-essay/review/mission/${submission?.missionId}?tab=missionEssay`
        ),
    },
  ];

  if (error) console.error('Error fetching mission essay data:', error);

  const processMissions = (missions = []) => {
    return missions
      .filter((mission) => mission.tasks?.length > 0)
      .sort((a, b) => (a.sequenceNumber || 0) - (b.sequenceNumber || 0))
      .flatMap((mission) =>
        mission.tasks.map((task) => {
          const weekValue =
            task.metaData?.week !== undefined
              ? parseInt(task.metaData.week)
              : null;
          const monthValue =
            task.metaData?.month !== undefined
              ? parseInt(task.metaData.month)
              : null;

          return {
            id: `${mission.id}-${task.id}`,
            missionId: mission.id,
            taskId: task.id,
            sequenceNumber: mission.sequenceNumber || 0,
            week: weekValue !== null ? `Week ${weekValue}` : '',
            month: monthValue !== null ? `Month ${monthValue}` : '',
            title: task.title || 'Untitled Mission',
            wordLimit: `${task.wordLimitMinimum} - ${task.wordLimitMaximum}`,
            deadline: `${task.deadline} ${
              task.deadline === 1 ? 'day' : 'days'
            }`,
            timeFrequency: mission.timeFrequency,
          };
        })
      );
  };

  const missions = processMissions(missionData?.items);
  const totalItems = missionData?.totalItems || 0;
  const totalPages = missionData?.totalPages || 1;

  const handlePageChange = (page) => setCurrentPage(page);
  const handleSort = (field) => {
    setSortField(field);
    setSortDirection((prev) =>
      field === sortField ? (prev === 'ASC' ? 'DESC' : 'ASC') : 'ASC'
    );
    setCurrentPage(1);
  };
  const handleTabChange = (index) => {
    setActiveTab(index);
    setCurrentPage(1);
    setSearchTerm('');
    setWeekOrMonth('');
    setSearchField('title'); // Reset to default search field instead of empty
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Mission Essay List</h1>
      </div>

      <div className="flex border-b border-gray-200 mb-6">
        {tabs.map((tab, index) => (
          <button
            key={index}
            className={`py-3 px-6 font-medium text-sm focus:outline-none ${
              activeTab === index
                ? 'text-black border-b-2 border-yellow-500 hover:bg-[#FEFCE8]'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => handleTabChange(index)}
          >
            {tab.name}
          </button>
        ))}
      </div>

      <NewTablePage
        title=""
        showCreateButton={false}
        columns={columns}
        actions={actions}
        data={missions}
        loading={loading}
        currentPage={currentPage}
        totalPages={totalPages}
        changePage={handlePageChange}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        showSearch={false}
        showNameFilter={false}
        showSortFilter={false}
        onSort={handleSort}
        sortField={sortField}
        sortDirection={sortDirection}
        showCheckboxes={false}
      />

      {missions.length === 0 && !loading && (
        <div className="text-center py-8 text-gray-500">
          No missions found.{' '}
          {searchTerm || weekOrMonth
            ? 'Try adjusting your search criteria.'
            : ''}
        </div>
      )}
    </div>
  );
};

export default MissionEssay;
